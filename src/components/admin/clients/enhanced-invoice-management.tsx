'use client'

import {
    AdjustmentsHorizontalIcon,
    ArrowDownIcon,
    ArrowUpIcon,
    CheckCircleIcon,
    ClockIcon,
    CurrencyDollarIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    FunnelIcon,
    ListBulletIcon,
    MagnifyingGlassIcon,
    PlusIcon,
    RectangleStackIcon,
    Squares2X2Icon,
    TrashIcon
} from '@heroicons/react/24/outline'
import { useEffect, useRef, useState } from 'react'
import { InvoiceRow } from './invoice-row'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
  contract?: {
    id: string | number
    contName: string
  }
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

interface EnhancedInvoiceManagementProps {
  client: Client
  project: Project
  selectedInvoice: Invoice | null
  onInvoiceSelect: (invoice: Invoice | null) => void
}

export function EnhancedInvoiceManagement({ client, project, selectedInvoice, onInvoiceSelect }: EnhancedInvoiceManagementProps) {
  // State management
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Enhanced controls state
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'amount', 'status', 'dueDate', 'description', 'taxAmount', 'updatedAt'
  ])
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnControls, setShowColumnControls] = useState(false)

  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  // Modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null)

  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Available columns configuration
  const availableColumns = [
    { key: 'amount', label: 'Amount', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'dueDate', label: 'Due Date', sortable: true },
    { key: 'description', label: 'Description', sortable: false },
    { key: 'subtotal', label: 'Subtotal', sortable: true },
    { key: 'taxAmount', label: 'Tax Amount', sortable: true },
    { key: 'paidAt', label: 'Paid Date', sortable: true },
    { key: 'createdAt', label: 'Created', sortable: true },
    { key: 'updatedAt', label: 'Updated', sortable: true }
  ]

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch invoices when dependencies change
  useEffect(() => {
    if (client && project) {
      fetchInvoices()
    }
  }, [client, project, debouncedSearchQuery, sortBy, sortOrder, filters, currentPage, pageSize])

  // Utility functions
  const fetchInvoices = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sortBy,
        sortOrder,
      })

      if (debouncedSearchQuery) {
        params.append('search', debouncedSearchQuery)
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      const response = await fetch(`/api/admin/projects/${project.id}/invoices?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch invoices: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setInvoices(data.data || [])
        setTotalPages(Math.ceil((data.total || 0) / pageSize))
      } else {
        throw new Error(data.error || 'Failed to fetch invoices')
      }
    } catch (err) {
      console.error('Error fetching invoices:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setInvoices([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async (data: any) => {
    try {
      setActionLoading('create')

      // First create the invoice
      const response = await fetch(`/api/admin/projects/${project.id}/invoices`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dueDate: data.dueDate,
          status: data.status,
          description: data.description,
          taxRate: data.taxRate,
          subtotal: data.subtotal,
          taxAmount: data.taxAmount,
          totalAmount: data.totalAmount,
          paidAt: data.paidAt
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error Response:', errorText)
        throw new Error(`Failed to create invoice: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      if (result.success) {
        const invoiceId = result.data.id

        // Now create the invoice items if any
        if (data.items && data.items.length > 0) {
          for (const item of data.items) {
            const itemResponse = await fetch(`/api/admin/invoices/${invoiceId}/items`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
              })
            })

            if (!itemResponse.ok) {
              console.error('Failed to create invoice item:', await itemResponse.text())
            }
          }
        }
      }

      await fetchInvoices()
      setIsCreateModalOpen(false)
    } catch (error) {
      console.error('Error creating invoice:', error)
      throw error
    } finally {
      setActionLoading(null)
    }
  }

  const handleUpdate = async (id: string, data: any) => {
    try {
      setActionLoading(`edit-${id}`)

      // Update the invoice
      const response = await fetch(`/api/admin/invoices/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dueDate: data.dueDate,
          status: data.status,
          description: data.description,
          taxRate: data.taxRate,
          subtotal: data.subtotal,
          taxAmount: data.taxAmount,
          totalAmount: data.totalAmount,
          paidAt: data.paidAt,
          items: data.items
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update invoice')
      }

      const result = await response.json()
      if (result.success) {
        // Handle invoice items update
        if (data.items && data.items.length > 0) {
          // For simplicity, we'll delete existing items and recreate them
          // In a production app, you might want to do a more sophisticated diff

          // Get existing items
          const itemsResponse = await fetch(`/api/admin/invoices/${id}/items`)
          if (itemsResponse.ok) {
            const itemsResult = await itemsResponse.json()
            if (itemsResult.success && itemsResult.data) {
              // Delete existing items
              for (const existingItem of itemsResult.data) {
                await fetch(`/api/admin/invoices/${id}/items/${existingItem.id}`, {
                  method: 'DELETE'
                })
              }
            }
          }

          // Create new items
          for (const item of data.items) {
            const itemResponse = await fetch(`/api/admin/invoices/${id}/items`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
              })
            })

            if (!itemResponse.ok) {
              console.error('Failed to create invoice item:', await itemResponse.text())
            }
          }
        }
      }

      await fetchInvoices()
      setIsEditModalOpen(false)
      setEditingInvoice(null)
    } catch (error) {
      console.error('Error updating invoice:', error)
      throw error
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this invoice?')) return

    try {
      setActionLoading(`delete-${id}`)
      const response = await fetch(`/api/admin/invoices/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete invoice')
      }

      await fetchInvoices()
      if (selectedInvoice?.id === id) {
        onInvoiceSelect(null)
      }
    } catch (error) {
      console.error('Error deleting invoice:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleAction = (action: string, invoice: Invoice) => {
    switch (action) {
      case 'edit':
        setEditingInvoice(invoice)
        setIsEditModalOpen(true)
        break
      case 'delete':
        handleDelete(String(invoice.id))
        break
      case 'download':
        // TODO: Implement download functionality
        console.log('Download invoice:', invoice.id)
        break
      default:
        break
    }
  }

  const handleBulkAction = async (action: string, invoiceIds: string[]) => {
    try {
      setActionLoading(`bulk-${action}`)

      const response = await fetch('/api/admin/invoices/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, invoiceIds })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} invoices`)
      }

      await fetchInvoices()
      setSelectedInvoices([])
    } catch (error) {
      console.error(`Error ${action} invoices:`, error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleSelectInvoice = (invoiceId: string) => {
    setSelectedInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    )
  }

  const handleSelectAll = () => {
    if (selectedInvoices.length === invoices.length) {
      setSelectedInvoices([])
    } else {
      setSelectedInvoices(invoices.map(invoice => String(invoice.id)))
    }
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Utility functions for formatting and status
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'overdue':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status.toLowerCase() !== 'paid'
  }

  const isDueSoon = (dueDate: string, status: string) => {
    const due = new Date(dueDate)
    const now = new Date()
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return due <= sevenDaysFromNow && due > now && status.toLowerCase() !== 'paid'
  }

  // Loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Invoices</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchInvoices}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Invoices for {project.name}</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage invoices and billing for this project ({client.companyName}) • {invoices.length} invoice{invoices.length !== 1 ? 's' : ''}
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Invoice
        </button>
      </div>

      {/* Enhanced Controls Bar */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search invoices..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-3">
              {/* Filters */}
              <div className="relative">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                    showFilters || Object.values(filters).some(v => v)
                      ? 'bg-blue-50 text-blue-700 border-blue-300'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  Filters
                  {Object.values(filters).some(v => v) && (
                    <span className="ml-2 bg-blue-100 text-blue-800 text-xs rounded-full px-2 py-0.5">
                      {Object.values(filters).filter(v => v).length}
                    </span>
                  )}
                </button>
              </div>

              {/* View Mode */}
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 text-sm font-medium transition-colors ${
                    viewMode === 'list'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                    viewMode === 'grid'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('card')}
                  className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                    viewMode === 'card'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Card view"
                >
                  <RectangleStackIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Density */}
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => setDisplayDensity('compact')}
                  className={`px-3 py-2 text-xs font-medium transition-colors ${
                    displayDensity === 'compact'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Compact
                </button>
                <button
                  onClick={() => setDisplayDensity('comfortable')}
                  className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                    displayDensity === 'comfortable'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Comfortable
                </button>
              </div>

              {/* Column Controls */}
              <div className="relative">
                <button
                  onClick={() => setShowColumnControls(!showColumnControls)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                  Columns
                </button>
                {showColumnControls && (
                  <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="p-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                      <div className="space-y-2">
                        {availableColumns.map((column) => (
                          <label key={column.key} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(column.key)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setVisibleColumns(prev => [...prev, column.key])
                                } else {
                                  setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                }
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All statuses</option>
                    <option value="pending">Pending</option>
                    <option value="sent">Sent</option>
                    <option value="paid">Paid</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Amount Range</label>
                  <select
                    value={filters.amountRange || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, amountRange: e.target.value }))}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All amounts</option>
                    <option value="0-1000">$0 - $1,000</option>
                    <option value="1000-5000">$1,000 - $5,000</option>
                    <option value="5000-10000">$5,000 - $10,000</option>
                    <option value="10000+">$10,000+</option>
                  </select>
                </div>
                <div className="flex items-end">
                  <button
                    onClick={() => setFilters({})}
                    className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                  >
                    Clear filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bulk Actions Bar */}
        {selectedInvoices.length > 0 && (
          <div className="px-6 py-3 bg-blue-50 border-t border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedInvoices.length} invoice{selectedInvoices.length !== 1 ? 's' : ''} selected
                </span>
                <button
                  onClick={() => setSelectedInvoices([])}
                  className="text-sm text-blue-700 hover:text-blue-900"
                >
                  Clear selection
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction('mark_sent', selectedInvoices)}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50"
                >
                  <DocumentTextIcon className="h-3 w-3 mr-1" />
                  Mark Sent
                </button>
                <button
                  onClick={() => handleBulkAction('mark_paid', selectedInvoices)}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50"
                >
                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                  Mark Paid
                </button>
                <button
                  onClick={() => {
                    if (confirm(`Are you sure you want to delete ${selectedInvoices.length} invoice${selectedInvoices.length !== 1 ? 's' : ''}?`)) {
                      handleBulkAction('delete', selectedInvoices)
                    }
                  }}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Overview */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{invoices.length}</div>
              <div className="text-sm text-gray-600">Total Invoices</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {invoices.filter(i => i.status === 'paid').length}
              </div>
              <div className="text-sm text-gray-600">Paid</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {invoices.filter(i => isOverdue(i.dueDate, i.status)).length}
              </div>
              <div className="text-sm text-gray-600">Overdue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {invoices.reduce((sum, i) => sum + i.totalAmount, 0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                })}
              </div>
              <div className="text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {invoices.length === 0 ? (
            <div className="text-center py-12">
              <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {debouncedSearchQuery || Object.values(filters).some(v => v)
                  ? 'Try adjusting your search or filters'
                  : 'Get started by creating a new invoice for this project'
                }
              </p>
              {!debouncedSearchQuery && !Object.values(filters).some(v => v) && (
                <div className="mt-6">
                  <button
                    onClick={() => setIsCreateModalOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Invoice
                  </button>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* List View */}
              {viewMode === 'list' && (
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left">
                          <input
                            type="checkbox"
                            checked={selectedInvoices.length === invoices.length && invoices.length > 0}
                            onChange={handleSelectAll}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </th>
                        {availableColumns
                          .filter(col => visibleColumns.includes(col.key))
                          .map((column) => (
                            <th
                              key={column.key}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => column.sortable && handleSort(column.key)}
                            >
                              <div className="flex items-center space-x-1">
                                <span>{column.label}</span>
                                {column.sortable && sortBy === column.key && (
                                  sortOrder === 'asc' ? (
                                    <ArrowUpIcon className="h-3 w-3" />
                                  ) : (
                                    <ArrowDownIcon className="h-3 w-3" />
                                  )
                                )}
                              </div>
                            </th>
                          ))}
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoices.map((invoice) => (
                        <InvoiceRow
                          key={invoice.id}
                          invoice={invoice}
                          isSelected={selectedInvoices.includes(String(invoice.id))}
                          onSelect={() => handleSelectInvoice(String(invoice.id))}
                          onAction={(action) => handleAction(action, invoice)}
                          onInvoiceSelect={onInvoiceSelect}
                          selectedInvoice={selectedInvoice}
                          visibleColumns={visibleColumns}
                          displayDensity={displayDensity}
                          viewMode={viewMode}
                          actionLoading={actionLoading}
                          formatDate={formatDate}
                          formatCurrency={formatCurrency}
                          getStatusColor={getStatusColor}
                          getStatusIcon={getStatusIcon}
                          isOverdue={isOverdue}
                          isDueSoon={isDueSoon}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {invoices.map((invoice) => (
                    <InvoiceRow
                      key={invoice.id}
                      invoice={invoice}
                      isSelected={selectedInvoices.includes(String(invoice.id))}
                      onSelect={() => handleSelectInvoice(String(invoice.id))}
                      onAction={(action) => handleAction(action, invoice)}
                      onInvoiceSelect={onInvoiceSelect}
                      selectedInvoice={selectedInvoice}
                      visibleColumns={visibleColumns}
                      displayDensity={displayDensity}
                      viewMode={viewMode}
                      actionLoading={actionLoading}
                      formatDate={formatDate}
                      formatCurrency={formatCurrency}
                      getStatusColor={getStatusColor}
                      getStatusIcon={getStatusIcon}
                      isOverdue={isOverdue}
                      isDueSoon={isDueSoon}
                    />
                  ))}
                </div>
              )}

              {/* Card View */}
              {viewMode === 'card' && (
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <InvoiceRow
                      key={invoice.id}
                      invoice={invoice}
                      isSelected={selectedInvoices.includes(String(invoice.id))}
                      onSelect={() => handleSelectInvoice(String(invoice.id))}
                      onAction={(action) => handleAction(action, invoice)}
                      onInvoiceSelect={onInvoiceSelect}
                      selectedInvoice={selectedInvoice}
                      visibleColumns={visibleColumns}
                      displayDensity={displayDensity}
                      viewMode={viewMode}
                      actionLoading={actionLoading}
                      formatDate={formatDate}
                      formatCurrency={formatCurrency}
                      getStatusColor={getStatusColor}
                      getStatusIcon={getStatusIcon}
                      isOverdue={isOverdue}
                      isDueSoon={isDueSoon}
                    />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">Show</span>
                    <select
                      value={pageSize}
                      onChange={(e) => {
                        setPageSize(Number(e.target.value))
                        setCurrentPage(1)
                      }}
                      className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                    <span className="text-sm text-gray-700">per page</span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`px-3 py-1 border rounded-md text-sm font-medium ${
                              currentPage === page
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        )
                      })}
                    </div>

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      {/* Create Invoice Modal */}
      {isCreateModalOpen && (() => {
        const { InvoiceModal } = require('./invoice-modal')
        return (
          <InvoiceModal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            onSubmit={handleCreate}
            title="Create New Invoice"
            client={client}
            project={project}
          />
        )
      })()}

      {/* Edit Invoice Modal */}
      {isEditModalOpen && (() => {
        const { InvoiceModal } = require('./invoice-modal')
        return (
          <InvoiceModal
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false)
              setEditingInvoice(null)
            }}
            onSubmit={(data) => editingInvoice && handleUpdate(editingInvoice.id, data)}
            title="Edit Invoice"
            initialData={editingInvoice}
            client={client}
            project={project}
          />
        )
      })()}
    </div>
  )
}
