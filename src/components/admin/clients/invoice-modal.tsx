'use client'

import {
    BuildingOfficeIcon,
    CalendarIcon,
    CurrencyDollarIcon,
    DocumentTextIcon,
    PlusIcon,
    TrashIcon,
    XMarkIcon
} from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import React, { useEffect, useState } from 'react'

interface InvoiceItem {
  id?: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  client: {
    id: string | number
    companyName: string
    contactName: string
    contactEmail: string
  }
  project: {
    id: string | number
    name: string
    description: string
  }
}

export function InvoiceModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  client,
  project
}: InvoiceModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    dueDate: '',
    status: 'DRAFT',
    description: '',
    taxRate: 0,
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0,
    paidAt: ''
  })

  const [items, setItems] = useState<InvoiceItem[]>([
    { description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }
  ])

  // Calculate totals when items or tax rate changes
  useEffect(() => {
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0)
    const taxAmount = (subtotal * formData.taxRate) / 100
    const totalAmount = subtotal + taxAmount

    setFormData(prev => ({
      ...prev,
      subtotal,
      taxAmount,
      totalAmount
    }))
  }, [items, formData.taxRate])

  // Load initial data
  useEffect(() => {
    const loadInvoiceData = async () => {
      if (initialData) {
        console.log('Initial invoice data:', initialData)
        setFormData({
          dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',
          status: initialData.status || 'DRAFT',
          description: initialData.description || '',
          taxRate: Number(initialData.taxRate) || 0,
          subtotal: Number(initialData.subtotal) || 0,
          taxAmount: Number(initialData.taxAmount) || 0,
          totalAmount: Number(initialData.totalAmount) || 0,
          paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''
        })

        // Load existing items from API
        try {
          const invoiceId = String(initialData.id)
          const response = await fetch(`/api/admin/invoices/${invoiceId}/items`)

          if (response.ok) {
            const result = await response.json()

            if (result.success && result.data && result.data.length > 0) {
              const mappedItems = result.data.map((item: any) => {
                // Handle different data types more robustly
                let quantity = 1
                let unitPrice = 0
                let totalPrice = 0

                // Convert quantity
                if (item.quantity !== null && item.quantity !== undefined) {
                  quantity = Number(item.quantity) || 1
                }

                // Convert unitprice - handle Prisma Decimal objects
                if (item.unitprice !== null && item.unitprice !== undefined) {
                  if (typeof item.unitprice === 'object') {
                    // Handle Prisma Decimal objects with structure {s, e, d}
                    if (item.unitprice.s !== undefined && item.unitprice.e !== undefined && item.unitprice.d && Array.isArray(item.unitprice.d)) {
                      // Decimal.js format: value = sign * digits * 10^(exponent - digits.length)
                      const sign = item.unitprice.s || 1
                      const exponent = item.unitprice.e || 0
                      const digits = item.unitprice.d || []
                      const digitsStr = digits.join('')
                      const digitsLength = digitsStr.length

                      // Calculate the actual value
                      const baseValue = parseInt(digitsStr) || 0
                      const actualExponent = exponent - digitsLength + 1
                      unitPrice = sign * baseValue * Math.pow(10, actualExponent)


                    } else if (item.unitprice.toNumber && typeof item.unitprice.toNumber === 'function') {
                      unitPrice = item.unitprice.toNumber()
                    } else if (item.unitprice.valueOf && typeof item.unitprice.valueOf === 'function') {
                      unitPrice = Number(item.unitprice.valueOf())
                    } else {
                      unitPrice = 0
                    }
                  } else {
                    unitPrice = Number(item.unitprice) || 0
                  }
                }

                // Convert totalprice - handle Prisma Decimal objects
                if (item.totalprice !== null && item.totalprice !== undefined) {
                  if (typeof item.totalprice === 'object') {
                    // Handle Prisma Decimal objects with structure {s, e, d}
                    if (item.totalprice.s !== undefined && item.totalprice.e !== undefined && item.totalprice.d && Array.isArray(item.totalprice.d)) {
                      // Decimal.js format: value = sign * digits * 10^(exponent - digits.length)
                      const sign = item.totalprice.s || 1
                      const exponent = item.totalprice.e || 0
                      const digits = item.totalprice.d || []
                      const digitsStr = digits.join('')
                      const digitsLength = digitsStr.length

                      // Calculate the actual value
                      const baseValue = parseInt(digitsStr) || 0
                      const actualExponent = exponent - digitsLength + 1
                      totalPrice = sign * baseValue * Math.pow(10, actualExponent)
                    } else if (item.totalprice.toNumber && typeof item.totalprice.toNumber === 'function') {
                      totalPrice = item.totalprice.toNumber()
                    } else if (item.totalprice.valueOf && typeof item.totalprice.valueOf === 'function') {
                      totalPrice = Number(item.totalprice.valueOf())
                    } else {
                      totalPrice = 0
                    }
                  } else {
                    totalPrice = Number(item.totalprice) || 0
                  }
                }

                // Fallback calculation if totalPrice is still 0
                if (totalPrice === 0 && unitPrice > 0 && quantity > 0) {
                  totalPrice = quantity * unitPrice
                }



                console.log('Mapping item:', {
                  raw: item,
                  rawUnitPrice: item.unitprice,
                  rawUnitPriceType: typeof item.unitprice,
                  rawTotalPrice: item.totalprice,
                  rawTotalPriceType: typeof item.totalprice,
                  mapped: { quantity, unitPrice, totalPrice }
                })

                return {
                  id: String(item.id),
                  description: String(item.description || ''),
                  quantity,
                  unitPrice,
                  totalPrice
                }
              })

              console.log('Setting items:', mappedItems)
              setItems(mappedItems)
            } else {
              // No items found, set default empty item
              setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
            }
          } else {
            // API error, set default empty item
            setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
          }
        } catch (error) {
          console.error('Error loading invoice items:', error)
          setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
        }
      } else {
        // Reset form for new invoice
        setFormData({
          dueDate: '',
          status: 'DRAFT',
          description: '',
          taxRate: 0,
          subtotal: 0,
          taxAmount: 0,
          totalAmount: 0,
          paidAt: ''
        })
        setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
      }
    }

    if (isOpen) {
      loadInvoiceData()
    }
  }, [initialData, isOpen])

  const addItem = () => {
    setItems([...items, { description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
  }

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index))
    }
  }

  const updateItem = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const newItems = [...items]
    newItems[index] = { ...newItems[index], [field]: value }

    // Auto-calculate total price when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      newItems[index].totalPrice = newItems[index].quantity * newItems[index].unitPrice
    }

    setItems(newItems)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate items
    const validItems = items.filter(item =>
      item.description.trim() && item.quantity > 0 && item.unitPrice > 0
    )

    if (validItems.length === 0) {
      alert('Please add at least one valid invoice item.')
      return
    }

    try {
      setLoading(true)

      const submitData = {
        clientId: client.id,
        projectId: project.id,
        dueDate: formData.dueDate,
        status: formData.status,
        description: formData.description,
        taxRate: formData.taxRate,
        subtotal: formData.subtotal,
        taxAmount: formData.taxAmount,
        totalAmount: formData.totalAmount,
        paidAt: formData.paidAt || null,
        items: validItems
      }

      console.log('Submitting invoice data:', submitData)
      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      alert('Failed to save invoice. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="relative bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">{title}</h2>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Client & Project Info */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <BuildingOfficeIcon className="h-4 w-4 mr-2 text-blue-600" />
                  Client & Project Information
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Client:</span>
                    <div className="text-gray-600">{client.companyName}</div>
                    <div className="text-gray-500">{client.contactName} • {client.contactEmail}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Project:</span>
                    <div className="text-gray-600">{project.name}</div>
                    <div className="text-gray-500">{project.description}</div>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-12 gap-6">
                <div className="col-span-8 space-y-4">
                  {/* Basic Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <DocumentTextIcon className="h-4 w-4 mr-2 text-gray-600" />
                      Invoice Information
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Due Date *
                        </label>
                        <input
                          type="date"
                          required
                          value={formData.dueDate}
                          onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Status *
                        </label>
                        <select
                          required
                          value={formData.status}
                          onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="DRAFT">Draft</option>
                          <option value="SENT">Sent</option>
                          <option value="PAID">Paid</option>
                          <option value="OVERDUE">Overdue</option>
                          <option value="CANCELLED">Cancelled</option>
                        </select>
                      </div>
                    </div>
                    <div className="mt-3">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Invoice description or notes"
                      />
                    </div>
                  </div>
                </div>

                {/* Summary Column */}
                <div className="col-span-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                      Invoice Summary
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span className="font-medium">${(formData.subtotal || 0).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax ({formData.taxRate || 0}%):</span>
                        <span className="font-medium">${(formData.taxAmount || 0).toFixed(2)}</span>
                      </div>
                      <div className="border-t border-green-200 pt-2 flex justify-between">
                        <span className="font-semibold text-gray-900">Total:</span>
                        <span className="font-bold text-green-600">${(formData.totalAmount || 0).toFixed(2)}</span>
                      </div>
                      {/* Debug info - remove this later */}
                      <div className="text-xs text-gray-500 border-t pt-2">
                        Debug: Items={items.length}, Subtotal={formData.subtotal}, Tax={formData.taxAmount}, Total={formData.totalAmount}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Invoice Items */}
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-900 flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                    Invoice Items
                  </h3>
                  <button
                    type="button"
                    onClick={addItem}
                    className="inline-flex items-center px-3 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-md hover:bg-purple-200 transition-colors"
                  >
                    <PlusIcon className="h-3 w-3 mr-1" />
                    Add Item
                  </button>
                </div>

                <div className="bg-white rounded-md border border-purple-200 overflow-hidden">
                  {/* Header Row */}
                  <div className="bg-gray-50 border-b border-gray-200 px-3 py-2">
                    <div className="grid grid-cols-12 gap-3 text-xs font-medium text-gray-700">
                      <div className="col-span-5">Description *</div>
                      <div className="col-span-2">Quantity *</div>
                      <div className="col-span-2">Unit Price *</div>
                      <div className="col-span-2">Total</div>
                      <div className="col-span-1">Action</div>
                    </div>
                  </div>

                  {/* Items */}
                  <div className="divide-y divide-gray-200">
                    {items.map((item, index) => (
                      <div key={index} className="p-3 hover:bg-gray-50 transition-colors">
                        <div className="grid grid-cols-12 gap-3 items-center">
                          <div className="col-span-5">
                            <input
                              type="text"
                              required
                              value={item.description}
                              onChange={(e) => updateItem(index, 'description', e.target.value)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                              placeholder="Item description"
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="number"
                              required
                              min="1"
                              step="1"
                              value={item.quantity}
                              onChange={(e) => updateItem(index, 'quantity', Number(e.target.value) || 1)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="number"
                              required
                              min="0"
                              step="0.01"
                              value={item.unitPrice === 0 ? '0' : (item.unitPrice || '')}
                              onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                              placeholder="0.00"
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="text"
                              value={`$${item.totalPrice.toFixed(2)}`}
                              readOnly
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50 text-gray-600 font-medium"
                            />
                          </div>
                          <div className="col-span-1 flex justify-center">
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              disabled={items.length === 1}
                              className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                              title="Remove item"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Tax and Payment Information */}
              <div className="grid grid-cols-2 gap-6">
                <div className="bg-orange-50 rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-2 text-orange-600" />
                    Tax Information
                  </h3>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={formData.taxRate}
                      onChange={(e) => setFormData({ ...formData, taxRate: Number(e.target.value) || 0 })}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2 text-yellow-600" />
                    Payment Information
                  </h3>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Payment Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={formData.paidAt}
                      onChange={(e) => setFormData({ ...formData, paidAt: e.target.value })}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  {initialData ? 'Editing existing invoice' : 'Creating new invoice'}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={loading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        {initialData ? 'Update Invoice' : 'Create Invoice'}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
