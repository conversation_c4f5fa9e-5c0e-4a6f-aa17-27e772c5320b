import { prisma } from '@/config/prisma'
import {
    ApiError,
    requireAdmin,
    successResponse,
    withE<PERSON>r<PERSON><PERSON><PERSON>
} from '@/services/api/api-utils'
import { NextRequest } from 'next/server'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/invoices/[id] - Get a specific invoice
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const invoice = await prisma.invoices.findUnique({
    where: { id },
    include: {
      clients: true,
      projects: true,
      orders: true,
      contracts: true,
      invoiceitems: true,
      payments: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  return successResponse(invoice)
})

// PUT /api/admin/invoices/[id] - Update an invoice
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received invoice update data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Get existing invoice to preserve foreign key relationships
  const existingInvoice = await prisma.invoices.findUnique({
    where: { id },
    select: {
      clientid: true,
      projectid: true,
      orderid: true,
      contid: true
    }
  })

  if (!existingInvoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Transform form data to database field names, preserving existing foreign keys
  const cleanInvoiceData = {
    // Preserve existing foreign key relationships
    clientid: existingInvoice.clientid,
    projectid: existingInvoice.projectid,
    orderid: existingInvoice.orderid,
    contid: existingInvoice.contid,
    // Update only the editable fields
    description: invoiceData.description,
    subtotal: invoiceData.subtotal ? Number(invoiceData.subtotal) : undefined,
    taxrate: invoiceData.taxRate ? Number(invoiceData.taxRate) : undefined,
    taxamount: invoiceData.taxAmount ? Number(invoiceData.taxAmount) : undefined,
    totalamount: invoiceData.totalAmount ? Number(invoiceData.totalAmount) : undefined,
    status: invoiceData.status,
    duedate: invoiceData.dueDate ? new Date(invoiceData.dueDate) : undefined,
    paidat: invoiceData.paidAt ? new Date(invoiceData.paidAt) : undefined,
  }

  // Remove undefined values
  const validatedData = Object.fromEntries(
    Object.entries(cleanInvoiceData).filter(([_, value]) => value !== undefined)
  )

  // Update invoice
  const invoice = await prisma.invoices.update({
    where: { id },
    data: validatedData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      projects: {
        select: {
          id: true,
          name: true,
          status: true
        }
      },
      contracts: {
        select: {
          id: true,
          contname: true,
          contstatus: true
        }
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true
        }
      },
      invoiceitems: true,
      payments: {
        select: {
          id: true,
          amount: true,
          status: true,
          paymentdate: true
        }
      }
    }
  })

  return successResponse(invoice, 'Invoice updated successfully')
})

// DELETE /api/admin/invoices/[id] - Delete an invoice
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  // Check if invoice has payments
  const paymentsCount = await prisma.payments.count({
    where: { invoiceid: id }
  })

  if (paymentsCount > 0) {
    throw new ApiError('Cannot delete invoice with associated payments', 400)
  }

  // Delete invoice items first
  await prisma.invoiceitems.deleteMany({
    where: { invoiceid: id }
  })

  // Then delete the invoice
  await prisma.invoices.delete({
    where: { id }
  })

  return successResponse(null, 'Invoice deleted successfully')
})
