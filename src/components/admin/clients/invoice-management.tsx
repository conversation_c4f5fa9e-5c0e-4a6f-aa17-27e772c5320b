'use client'

import {
    ArrowDownTrayIcon,
    CalendarIcon,
    CheckCircleIcon,
    ClockIcon,
    CurrencyDollarIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    MagnifyingGlassIcon,
    PencilIcon,
    TrashIcon
} from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { InvoiceModal } from './invoice-modal'

// Import the invoice modal

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
  contract?: {
    id: string | number
    contName: string
  }
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

interface InvoiceManagementProps {
  client: Client
  project: Project
  selectedInvoice: Invoice | null
  onInvoiceSelect: (invoice: Invoice | null) => void
}

export function InvoiceManagement({ client, project, selectedInvoice, onInvoiceSelect }: InvoiceManagementProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null)

  useEffect(() => {
    if (client && project) {
      fetchInvoices()
    }
  }, [client, project])

  const fetchInvoices = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`/api/admin/projects/${project.id}/invoices?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch invoices: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setInvoices(data.data || [])
      } else {
        throw new Error(data.error || 'Failed to fetch invoices')
      }
    } catch (err) {
      console.error('Error fetching invoices:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setInvoices([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchInvoices()
  }

  const handleCreateInvoice = async (data: any) => {
    try {
      // First create the invoice
      const response = await fetch(`/api/admin/projects/${project.id}/invoices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dueDate: data.dueDate,
          status: data.status,
          description: data.description,
          taxRate: data.taxRate,
          subtotal: data.subtotal,
          taxAmount: data.taxAmount,
          totalAmount: data.totalAmount,
          paidAt: data.paidAt
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to create invoice: ${response.statusText}`)
      }

      const result = await response.json()
      if (result.success) {
        const invoiceId = result.data.id

        // Now create the invoice items if any
        if (data.items && data.items.length > 0) {
          for (const item of data.items) {
            const itemResponse = await fetch(`/api/admin/invoices/${invoiceId}/items`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
              }),
            })

            if (!itemResponse.ok) {
              console.error('Failed to create invoice item:', await itemResponse.text())
            }
          }
        }

        await fetchInvoices() // Refresh the list
        setIsCreateModalOpen(false)
      } else {
        throw new Error(result.error || 'Failed to create invoice')
      }
    } catch (error) {
      console.error('Error creating invoice:', error)
      throw error
    }
  }

  const handleEditInvoice = async (data: any) => {
    if (!editingInvoice) return

    try {
      // Update the invoice
      const response = await fetch(`/api/admin/invoices/${editingInvoice.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dueDate: data.dueDate,
          status: data.status,
          description: data.description,
          taxRate: data.taxRate,
          subtotal: data.subtotal,
          taxAmount: data.taxAmount,
          totalAmount: data.totalAmount,
          paidAt: data.paidAt,
          items: data.items
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update invoice: ${response.statusText}`)
      }

      const result = await response.json()
      if (result.success) {
        // Handle invoice items update
        if (data.items && data.items.length > 0) {
          // For simplicity, we'll delete existing items and recreate them
          // In a production app, you might want to do a more sophisticated diff

          // Get existing items
          const itemsResponse = await fetch(`/api/admin/invoices/${editingInvoice.id}/items`)
          if (itemsResponse.ok) {
            const itemsResult = await itemsResponse.json()
            if (itemsResult.success && itemsResult.data) {
              // Delete existing items
              for (const existingItem of itemsResult.data) {
                await fetch(`/api/admin/invoices/${editingInvoice.id}/items/${existingItem.id}`, {
                  method: 'DELETE'
                })
              }
            }
          }

          // Create new items
          for (const item of data.items) {
            const itemResponse = await fetch(`/api/admin/invoices/${editingInvoice.id}/items`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
              }),
            })

            if (!itemResponse.ok) {
              console.error('Failed to create invoice item:', await itemResponse.text())
            }
          }
        }

        await fetchInvoices() // Refresh the list
        setIsEditModalOpen(false)
        setEditingInvoice(null)
      } else {
        throw new Error(result.error || 'Failed to update invoice')
      }
    } catch (error) {
      console.error('Error updating invoice:', error)
      throw error
    }
  }

  const handleDeleteInvoice = async (invoiceId: string | number) => {
    if (!confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/invoices/${invoiceId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error(`Failed to delete invoice: ${response.statusText}`)
      }

      const result = await response.json()
      if (result.success) {
        await fetchInvoices() // Refresh the list
        if (selectedInvoice?.id === invoiceId) {
          onInvoiceSelect(null) // Clear selection if deleted invoice was selected
        }
      } else {
        throw new Error(result.error || 'Failed to delete invoice')
      }
    } catch (error) {
      console.error('Error deleting invoice:', error)
      alert('Failed to delete invoice. Please try again.')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'overdue':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status.toLowerCase() !== 'paid'
  }

  const isDueSoon = (dueDate: string, status: string) => {
    const due = new Date(dueDate)
    const now = new Date()
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return due <= sevenDaysFromNow && due > now && status.toLowerCase() !== 'paid'
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Invoices</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchInvoices}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Invoices for {project.name}</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage invoices and billing for this project ({client.companyName})
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <CurrencyDollarIcon className="h-4 w-4 mr-2" />
            Add Invoice
          </button>
          <Link
            href="/admin/invoices"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <DocumentTextIcon className="h-4 w-4 mr-2" />
            View All Invoices
          </Link>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search invoices..."
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Search
          </button>
        </form>
      </div>

      {/* Invoices List */}
      {invoices.length === 0 ? (
        <div className="text-center py-12">
          <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Try adjusting your search terms.' : 'This project doesn\'t have any invoices yet.'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {invoices.map((invoice) => (
            <motion.div
              key={invoice.id}
              onClick={() => onInvoiceSelect(invoice)}
              className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedInvoice?.id === invoice.id
                  ? 'border-yellow-500 bg-yellow-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      {formatCurrency(invoice.totalAmount)}
                    </h3>

                    {/* Status Badge */}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                      {getStatusIcon(invoice.status)}
                      <span className="ml-1">{invoice.status}</span>
                    </span>

                    {/* Overdue Warning */}
                    {isOverdue(invoice.dueDate, invoice.status) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                        Overdue
                      </span>
                    )}

                    {/* Due Soon Warning */}
                    {isDueSoon(invoice.dueDate, invoice.status) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Due Soon
                      </span>
                    )}
                  </div>

                  {/* Invoice Description */}
                  {invoice.description && (
                    <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                      {invoice.description}
                    </p>
                  )}

                  {/* Invoice Details */}
                  <div className="mt-4 overflow-x-auto">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm min-w-[640px] sm:min-w-0">
                    <div className="flex items-center text-gray-600">
                      <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
                      <span>Due {formatDate(invoice.dueDate)}</span>
                    </div>

                    {invoice.subtotal && (
                      <div className="flex items-center text-gray-600">
                        <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                        <span>Subtotal: {formatCurrency(invoice.subtotal)}</span>
                      </div>
                    )}

                    {invoice.taxAmount > 0 && (
                      <div className="flex items-center text-gray-600">
                        <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                        <span>Tax: {formatCurrency(invoice.taxAmount)} ({invoice.taxRate}%)</span>
                      </div>
                    )}

                    {invoice.paidAt && (
                      <div className="flex items-center text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 mr-2 text-green-600" />
                        <span>Paid {formatDate(invoice.paidAt)}</span>
                      </div>
                    )}
                    </div>
                  </div>

                  {/* Related Contract/Project/Order */}
                  {(invoice.contract || invoice.project || invoice.order) && (
                    <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-gray-500">
                      {invoice.contract && (
                        <div>
                          <span className="font-medium">Contract:</span> {invoice.contract.contName}
                        </div>
                      )}
                      {invoice.project && (
                        <div>
                          <span className="font-medium">Project:</span> {invoice.project.name}
                        </div>
                      )}
                      {invoice.order && (
                        <div>
                          <span className="font-medium">Order:</span> {invoice.order.orderTitle}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Invoice Amount Breakdown */}
                <div className="ml-6 text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatCurrency(invoice.totalAmount)}
                  </div>
                  {invoice.subtotal && invoice.subtotal !== invoice.totalAmount && (
                    <div className="text-sm text-gray-500">
                      Subtotal: {formatCurrency(invoice.subtotal)}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex items-center justify-end space-x-2 mt-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setEditingInvoice(invoice)
                        setIsEditModalOpen(true)
                      }}
                      className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                      title="Edit Invoice"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        // TODO: Download invoice
                        console.log('Download invoice:', invoice.id)
                      }}
                      className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
                      title="Download Invoice"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteInvoice(invoice.id)
                      }}
                      className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                      title="Delete Invoice"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Invoice Dates */}
              <div className="mt-6 border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div>
                    Created {formatDate(invoice.createdAt)}
                  </div>
                  {invoice.updatedAt && (
                    <div>
                      Updated {formatDate(invoice.updatedAt)}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Create Invoice Modal */}
      <InvoiceModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateInvoice}
        title="Create New Invoice"
        client={client}
        project={project}
      />

      {/* Edit Invoice Modal */}
      <InvoiceModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingInvoice(null)
        }}
        onSubmit={handleEditInvoice}
        title="Edit Invoice"
        initialData={editingInvoice}
        client={client}
        project={project}
      />
    </div>
  )
}
